#!/bin/bash
echo "正在启动 OTA 设备管理系统（后台模式）…"

# 设置环境变量
export FLASK_ENV=production
export DATABASE_URL="postgresql://kafanglinlin:7jbWNHYZZLMa@localhost:5432/KafangCharging"

# 切换到脚本所在目录
cd "$(dirname "$0")"

# 启动应用（生产环境配置：host=0.0.0.0, port=5000, debug=False）
nohup python3 app.py --host 0.0.0.0 --port 5000 --no-debug > output.log 2>&1 &

echo "已启动，日志输出在 output.log"
echo "服务地址: http://0.0.0.0:5000"
echo "调试模式: 关闭"